import Medusa from "@medusajs/js-sdk"
import { browser } from '$app/environment'

// Defaults to standard port for Medusa server
let MEDUSA_BACKEND_URL = "http://localhost:9000"

if (browser && window.location.hostname !== 'localhost') {
  // In production, use the same domain
  MEDUSA_BACKEND_URL = `${window.location.protocol}//${window.location.hostname}:9000`
}

// Override with environment variable if available
if (typeof process !== 'undefined' && process.env?.MEDUSA_BACKEND_URL) {
  MEDUSA_BACKEND_URL = process.env.MEDUSA_BACKEND_URL
}

export const sdk = new Medusa({
  baseUrl: MEDUSA_BACKEND_URL,
  debug: true, // Enable debug in development
  publishableKey: process.env.MEDUSA_PUBLISHABLE_KEY,
})

export const BACKEND_URL = MEDUSA_BACKEND_URL
