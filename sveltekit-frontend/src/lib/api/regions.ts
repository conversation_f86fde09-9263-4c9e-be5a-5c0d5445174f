import { sdk } from '$lib/config'
import type { Region } from '$lib/types'

export class RegionsAPI {
  // Get all regions
  static async listRegions(): Promise<Region[]> {
    try {
      const response = await sdk.store.region.list()
      return response.regions
    } catch (error) {
      console.error('Error fetching regions:', error)
      throw error
    }
  }

  // Get region by ID
  static async getRegion(id: string): Promise<Region> {
    try {
      const response = await sdk.store.region.retrieve(id)
      return response.region
    } catch (error) {
      console.error('Error fetching region:', error)
      throw error
    }
  }

  // Get region by country code
  static async getRegionByCountry(countryCode: string): Promise<Region | null> {
    try {
      const regions = await this.listRegions()
      return regions.find(region => 
        region.countries?.some(country => 
          country.iso_2?.toLowerCase() === countryCode.toLowerCase()
        )
      ) || null
    } catch (error) {
      console.error('Error fetching region by country:', error)
      return null
    }
  }
}
