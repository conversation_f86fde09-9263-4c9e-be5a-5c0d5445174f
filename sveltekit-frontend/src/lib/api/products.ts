import { sdk } from '$lib/config'
import type { Product, ProductFilters, PaginationParams, ProductListResponse } from '$lib/types'

export class ProductsAPI {
  // List products with filters and pagination
  static async listProducts(
    regionId: string,
    filters: ProductFilters = {},
    pagination: PaginationParams = {}
  ): Promise<ProductListResponse> {
    try {
      const { limit = 12, offset = 0 } = pagination
      
      const response = await sdk.client.fetch<{ products: Product[]; count: number }>(
        `/store/products`,
        {
          method: "GET",
          query: {
            limit,
            offset,
            region_id: regionId,
            fields: "*variants.calculated_price,+variants.inventory_quantity,+metadata,+tags",
            ...filters,
          },
        }
      )

      const { products, count } = response
      const nextPage = count > offset + limit ? Math.floor(offset / limit) + 2 : null

      return {
        products,
        count,
        nextPage,
      }
    } catch (error) {
      console.error('Error fetching products:', error)
      throw error
    }
  }

  // Get single product by ID
  static async getProduct(id: string, regionId: string): Promise<Product> {
    try {
      const response = await sdk.store.product.retrieve(id, {
        region_id: regionId,
        fields: "*variants.calculated_price,+variants.inventory_quantity,+metadata,+tags,+images",
      })
      
      return response.product
    } catch (error) {
      console.error('Error fetching product:', error)
      throw error
    }
  }

  // Search products
  static async searchProducts(
    query: string,
    regionId: string,
    pagination: PaginationParams = {}
  ): Promise<ProductListResponse> {
    return this.listProducts(regionId, { q: query }, pagination)
  }

  // Get products by category
  static async getProductsByCategory(
    categoryId: string,
    regionId: string,
    pagination: PaginationParams = {}
  ): Promise<ProductListResponse> {
    return this.listProducts(regionId, { category_id: [categoryId] }, pagination)
  }

  // Get products by collection
  static async getProductsByCollection(
    collectionId: string,
    regionId: string,
    pagination: PaginationParams = {}
  ): Promise<ProductListResponse> {
    return this.listProducts(regionId, { collection_id: [collectionId] }, pagination)
  }

  // Get featured products (you can customize this logic)
  static async getFeaturedProducts(
    regionId: string,
    limit: number = 8
  ): Promise<Product[]> {
    try {
      const response = await this.listProducts(regionId, {}, { limit, offset: 0 })
      return response.products
    } catch (error) {
      console.error('Error fetching featured products:', error)
      throw error
    }
  }
}
