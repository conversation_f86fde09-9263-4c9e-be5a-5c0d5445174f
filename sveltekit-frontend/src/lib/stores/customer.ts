import { writable, derived } from 'svelte/store'
import { browser } from '$app/environment'
import { sdk } from '$lib/config'
import type { Customer, CustomerState } from '$lib/types'

// Customer state store
const createCustomerStore = () => {
  const { subscribe, set, update } = writable<CustomerState>({
    customer: null,
    isAuthenticated: false,
    isLoading: false,
    error: null
  })

  return {
    subscribe,
    
    // Initialize customer (check if authenticated)
    async initialize() {
      update(state => ({ ...state, isLoading: true, error: null }))
      
      try {
        const token = browser ? localStorage.getItem('auth_token') : null
        
        if (token) {
          // Try to get current customer
          const response = await sdk.store.customer.retrieve()
          const customer = response.customer
          
          set({
            customer,
            isAuthenticated: true,
            isLoading: false,
            error: null
          })
          
          return customer
        } else {
          set({
            customer: null,
            isAuthenticated: false,
            isLoading: false,
            error: null
          })
          return null
        }
      } catch (error) {
        // Token might be invalid, clear it
        if (browser) {
          localStorage.removeItem('auth_token')
        }
        
        set({
          customer: null,
          isAuthenticated: false,
          isLoading: false,
          error: null
        })
        return null
      }
    },

    // Login
    async login(email: string, password: string) {
      update(state => ({ ...state, isLoading: true, error: null }))
      
      try {
        const token = await sdk.auth.login("customer", "emailpass", { email, password })
        
        if (browser && token) {
          localStorage.setItem('auth_token', token as string)
        }

        // Get customer details
        const response = await sdk.store.customer.retrieve()
        const customer = response.customer

        set({
          customer,
          isAuthenticated: true,
          isLoading: false,
          error: null
        })

        return customer
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Login failed'
        set({
          customer: null,
          isAuthenticated: false,
          isLoading: false,
          error: errorMessage
        })
        throw error
      }
    },

    // Register
    async register(customerData: {
      email: string
      password: string
      first_name: string
      last_name: string
      phone?: string
    }) {
      update(state => ({ ...state, isLoading: true, error: null }))
      
      try {
        // Register with auth
        const token = await sdk.auth.register("customer", "emailpass", {
          email: customerData.email,
          password: customerData.password,
        })

        if (browser && token) {
          localStorage.setItem('auth_token', token as string)
        }

        // Create customer profile
        const response = await sdk.store.customer.create({
          email: customerData.email,
          first_name: customerData.first_name,
          last_name: customerData.last_name,
          phone: customerData.phone,
        })

        const customer = response.customer

        set({
          customer,
          isAuthenticated: true,
          isLoading: false,
          error: null
        })

        return customer
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Registration failed'
        set({
          customer: null,
          isAuthenticated: false,
          isLoading: false,
          error: errorMessage
        })
        throw error
      }
    },

    // Logout
    async logout() {
      update(state => ({ ...state, isLoading: true, error: null }))
      
      try {
        await sdk.auth.logout()
        
        if (browser) {
          localStorage.removeItem('auth_token')
        }

        set({
          customer: null,
          isAuthenticated: false,
          isLoading: false,
          error: null
        })
      } catch (error) {
        // Even if logout fails on server, clear local state
        if (browser) {
          localStorage.removeItem('auth_token')
        }

        set({
          customer: null,
          isAuthenticated: false,
          isLoading: false,
          error: null
        })
      }
    },

    // Update customer
    async updateCustomer(updates: Partial<Customer>) {
      update(state => ({ ...state, isLoading: true, error: null }))
      
      try {
        const response = await sdk.store.customer.update(updates)
        const customer = response.customer

        update(state => ({
          ...state,
          customer,
          isLoading: false,
          error: null
        }))

        return customer
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Update failed'
        update(state => ({ ...state, isLoading: false, error: errorMessage }))
        throw error
      }
    },

    // Clear error
    clearError() {
      update(state => ({ ...state, error: null }))
    }
  }
}

export const customerStore = createCustomerStore()

// Derived stores for convenience
export const customer = derived(customerStore, $customerStore => $customerStore.customer)
export const isAuthenticated = derived(customerStore, $customerStore => $customerStore.isAuthenticated)
export const customerIsLoading = derived(customerStore, $customerStore => $customerStore.isLoading)
export const customerError = derived(customerStore, $customerStore => $customerStore.error)
