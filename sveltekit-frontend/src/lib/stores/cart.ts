import { writable, derived } from 'svelte/store'
import { browser } from '$app/environment'
import { sdk } from '$lib/config'
import type { Cart, CartState, AddToCartParams, UpdateCartItemParams } from '$lib/types'

// Cart state store
const createCartStore = () => {
  const { subscribe, set, update } = writable<CartState>({
    cart: null,
    isLoading: false,
    error: null
  })

  return {
    subscribe,
    
    // Initialize cart (get existing or create new)
    async initialize(regionId?: string) {
      update(state => ({ ...state, isLoading: true, error: null }))
      
      try {
        let cartId = browser ? localStorage.getItem('cart_id') : null
        let cart: Cart | null = null

        // Try to retrieve existing cart
        if (cartId) {
          try {
            const response = await sdk.store.cart.retrieve(cartId)
            cart = response.cart
          } catch (error) {
            // Cart doesn't exist, clear the stored ID
            if (browser) localStorage.removeItem('cart_id')
            cartId = null
          }
        }

        // Create new cart if none exists
        if (!cart && regionId) {
          const response = await sdk.store.cart.create({ region_id: regionId })
          cart = response.cart
          if (browser && cart) {
            localStorage.setItem('cart_id', cart.id)
          }
        }

        set({ cart, isLoading: false, error: null })
        return cart
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Failed to initialize cart'
        set({ cart: null, isLoading: false, error: errorMessage })
        throw error
      }
    },

    // Add item to cart
    async addItem({ variantId, quantity }: AddToCartParams) {
      update(state => ({ ...state, isLoading: true, error: null }))
      
      try {
        const currentState = await new Promise<CartState>(resolve => {
          const unsubscribe = subscribe(state => {
            unsubscribe()
            resolve(state)
          })
        })

        if (!currentState.cart) {
          throw new Error('No cart available')
        }

        const response = await sdk.store.cart.createLineItem(
          currentState.cart.id,
          {
            variant_id: variantId,
            quantity
          }
        )

        update(state => ({ ...state, cart: response.cart, isLoading: false }))
        return response.cart
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Failed to add item to cart'
        update(state => ({ ...state, isLoading: false, error: errorMessage }))
        throw error
      }
    },

    // Update cart item quantity
    async updateItem({ lineId, quantity }: UpdateCartItemParams) {
      update(state => ({ ...state, isLoading: true, error: null }))
      
      try {
        const currentState = await new Promise<CartState>(resolve => {
          const unsubscribe = subscribe(state => {
            unsubscribe()
            resolve(state)
          })
        })

        if (!currentState.cart) {
          throw new Error('No cart available')
        }

        const response = await sdk.store.cart.updateLineItem(
          currentState.cart.id,
          lineId,
          { quantity }
        )

        update(state => ({ ...state, cart: response.cart, isLoading: false }))
        return response.cart
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Failed to update cart item'
        update(state => ({ ...state, isLoading: false, error: errorMessage }))
        throw error
      }
    },

    // Remove item from cart
    async removeItem(lineId: string) {
      update(state => ({ ...state, isLoading: true, error: null }))
      
      try {
        const currentState = await new Promise<CartState>(resolve => {
          const unsubscribe = subscribe(state => {
            unsubscribe()
            resolve(state)
          })
        })

        if (!currentState.cart) {
          throw new Error('No cart available')
        }

        const response = await sdk.store.cart.deleteLineItem(currentState.cart.id, lineId)
        update(state => ({ ...state, cart: response.cart, isLoading: false }))
        return response.cart
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Failed to remove cart item'
        update(state => ({ ...state, isLoading: false, error: errorMessage }))
        throw error
      }
    },

    // Clear cart
    clear() {
      if (browser) {
        localStorage.removeItem('cart_id')
      }
      set({ cart: null, isLoading: false, error: null })
    },

    // Clear error
    clearError() {
      update(state => ({ ...state, error: null }))
    }
  }
}

export const cartStore = createCartStore()

// Derived stores for convenience
export const cart = derived(cartStore, $cartStore => $cartStore.cart)
export const cartItemsCount = derived(cart, $cart => 
  $cart?.items?.reduce((total, item) => total + (item.quantity || 0), 0) || 0
)
export const cartTotal = derived(cart, $cart => $cart?.total || 0)
export const cartIsLoading = derived(cartStore, $cartStore => $cartStore.isLoading)
export const cartError = derived(cartStore, $cartStore => $cartStore.error)
