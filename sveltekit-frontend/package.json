{"name": "sveltekit-frontend", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite dev --port 8000", "build": "vite build", "preview": "vite preview", "prepare": "svelte-kit sync || echo ''", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch"}, "devDependencies": {"@sveltejs/adapter-auto": "^6.0.0", "@sveltejs/kit": "^2.16.0", "@sveltejs/vite-plugin-svelte": "^5.0.0", "svelte": "^5.0.0", "svelte-check": "^4.0.0", "typescript": "^5.0.0", "vite": "^6.2.6"}, "dependencies": {"@medusajs/js-sdk": "^0.0.1", "@tailwindcss/postcss": "^4.1.7", "@tailwindcss/typography": "^0.5.16", "autoprefixer": "^10.4.21", "clsx": "^2.1.1", "lucide-svelte": "^0.511.0", "postcss": "^8.5.3", "tailwindcss": "^4.1.7"}}